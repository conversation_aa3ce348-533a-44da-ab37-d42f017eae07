#!/usr/bin/env python3
"""
WNN-MRNN模型参数量计算脚本

这个脚本用于快速计算不同参数组合下的WNN-MRNN模型参数量，
避免每次都加载数据集，提高计算效率。

使用方法：python calculate_parameters.py
"""

import os
import sys
import yaml
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from itertools import product
import json
from datetime import datetime

# 导入模型
from models.wnn_mrnn import WNN_MRNN

def count_trainable_parameters(model):
    """计算模型的可训练参数数量"""
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    total_params = sum(p.numel() for p in model.parameters())
    return trainable_params, total_params

def load_config(config_path='config.yaml'):
    """加载配置文件"""
    with open(config_path, 'r', encoding='utf-8') as f:
        config = yaml.safe_load(f)
    return config

def create_model_with_params(wavelet_dim, rnn_dim, num_levels, num_layers,
                           sequence_length, num_classes=11, config=None):
    """
    根据给定参数创建模型

    Args:
        wavelet_dim: 小波维度
        rnn_dim: RNN维度
        num_levels: 小波分解层数
        num_layers: MMRNN层数
        sequence_length: 序列长度
        num_classes: 分类类别数
        config: 配置文件

    Returns:
        model: 创建的模型
    """
    if config is None:
        # 使用默认配置
        model_config = {
            'in_channels': 2,
            'msb_depth': 1,
            'd_state': 16,
            'd_conv': 4,
            'expand': 2,
            'dropout': 0.1
        }
    else:
        model_config = config['model']

    # 创建模型
    model = WNN_MRNN(
        in_channels=model_config['in_channels'],
        num_classes=num_classes,
        wavelet_dim=wavelet_dim,
        rnn_dim=rnn_dim,
        num_layers=num_layers,
        num_levels=num_levels,
        d_state=model_config['d_state'],
        msb_depth=model_config['msb_depth'],
        drop_rate=model_config.get('dropout', 0.1),
        d_conv=model_config['d_conv'],
        expand=model_config['expand']
    )

    return model

def calculate_parameters_for_combinations():
    """计算所有参数组合的参数量"""
    
    # 参数组合
    param_combinations = {
        'wavelet_dim': [32, 64, 128, 256],
        'rnn_dim': [32, 64, 128, 256],
        'num_levels': [1, 2, 3, 4],
        'num_layers': [1, 2, 3, 4],
    }
    
    # 数据集配置
    datasets_config = {
        'rml': {'sequence_length': 128, 'num_classes': 11},
        'rml201801a': {'sequence_length': 1024, 'num_classes': 24},
        'hisar': {'sequence_length': 1024, 'num_classes': 26},
        'torchsig1024': {'sequence_length': 1024, 'num_classes': 25},
        'torchsig2048': {'sequence_length': 2048, 'num_classes': 25},
        'torchsig4096': {'sequence_length': 4096, 'num_classes': 25},
    }
    
    # 加载配置文件
    try:
        config = load_config()
        print("成功加载配置文件")
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        print("使用默认配置")
        config = None
    
    # 存储结果
    results = []
    
    # 生成所有参数组合
    param_names = list(param_combinations.keys())
    param_values = list(param_combinations.values())
    
    total_combinations = len(list(product(*param_values))) * len(datasets_config)
    current_combination = 0
    
    print(f"开始计算参数量，总共 {total_combinations} 种组合...")
    print("=" * 80)
    
    # 遍历所有数据集
    for dataset_name, dataset_info in datasets_config.items():
        sequence_length = dataset_info['sequence_length']
        num_classes = dataset_info['num_classes']
        
        print(f"\n数据集: {dataset_name}")
        print(f"序列长度: {sequence_length}, 类别数: {num_classes}")
        print("-" * 60)
        
        # 遍历所有参数组合
        for combination in product(*param_values):
            current_combination += 1
            
            # 创建参数字典
            params = dict(zip(param_names, combination))
            
            try:
                # 创建模型
                model = create_model_with_params(
                    wavelet_dim=params['wavelet_dim'],
                    rnn_dim=params['rnn_dim'],
                    num_levels=params['num_levels'],
                    num_layers=params['num_layers'],
                    sequence_length=sequence_length,
                    num_classes=num_classes,
                    config=config
                )
                
                # 计算参数量
                trainable_params, total_params = count_trainable_parameters(model)
                
                # 存储结果
                result = {
                    'dataset': dataset_name,
                    'sequence_length': sequence_length,
                    'num_classes': num_classes,
                    'wavelet_dim': params['wavelet_dim'],
                    'rnn_dim': params['rnn_dim'],
                    'num_levels': params['num_levels'],
                    'num_layers': params['num_layers'],
                    'trainable_params': trainable_params,
                    'total_params': total_params,
                    'trainable_ratio': trainable_params / total_params if total_params > 0 else 0
                }
                results.append(result)
                
                # 打印进度
                if current_combination % 20 == 0 or current_combination <= 10:
                    print(f"[{current_combination:4d}/{total_combinations}] "
                          f"wavelet_dim={params['wavelet_dim']:3d}, rnn_dim={params['rnn_dim']:3d}, "
                          f"num_levels={params['num_levels']}, num_layers={params['num_layers']} -> "
                          f"参数量: {trainable_params:,}")
                
            except Exception as e:
                print(f"计算失败 - 参数组合: {params}, 错误: {e}")
                continue
    
    print("\n" + "=" * 80)
    print("参数量计算完成！")

    return results

def save_results_to_files(results):
    """保存结果到文件"""

    # 创建输出目录
    output_dir = 'parameter_analysis'
    os.makedirs(output_dir, exist_ok=True)

    # 转换为DataFrame
    df = pd.DataFrame(results)

    # 生成时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 保存详细结果到CSV
    csv_file = os.path.join(output_dir, f'parameter_analysis_detailed_{timestamp}.csv')
    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
    print(f"详细结果已保存到: {csv_file}")

    # 保存结果到JSON
    json_file = os.path.join(output_dir, f'parameter_analysis_detailed_{timestamp}.json')
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    print(f"JSON结果已保存到: {json_file}")

    return df, csv_file, json_file

def analyze_results(df):
    """分析结果并生成统计信息"""

    print("\n" + "=" * 80)
    print("参数量分析统计")
    print("=" * 80)

    # 按数据集分组统计
    print("\n1. 按数据集统计:")
    print("-" * 40)
    dataset_stats = df.groupby('dataset')['trainable_params'].agg(['min', 'max', 'mean', 'std'])
    for dataset in dataset_stats.index:
        stats = dataset_stats.loc[dataset]
        print(f"{dataset:12s}: 最小={stats['min']:8,} 最大={stats['max']:9,} "
              f"平均={stats['mean']:8,.0f} 标准差={stats['std']:8,.0f}")

    # 参数影响分析
    print("\n2. 各参数对参数量的影响:")
    print("-" * 40)

    # 分析wavelet_dim的影响
    wavelet_impact = df.groupby('wavelet_dim')['trainable_params'].mean()
    print("wavelet_dim影响:")
    for dim, params in wavelet_impact.items():
        print(f"  {dim:3d} -> 平均参数量: {params:8,.0f}")

    # 分析rnn_dim的影响
    rnn_impact = df.groupby('rnn_dim')['trainable_params'].mean()
    print("rnn_dim影响:")
    for dim, params in rnn_impact.items():
        print(f"  {dim:3d} -> 平均参数量: {params:8,.0f}")

    # 分析num_levels的影响
    levels_impact = df.groupby('num_levels')['trainable_params'].mean()
    print("num_levels影响:")
    for levels, params in levels_impact.items():
        print(f"  {levels:3d} -> 平均参数量: {params:8,.0f}")

    # 分析num_layers的影响
    layers_impact = df.groupby('num_layers')['trainable_params'].mean()
    print("num_layers影响:")
    for layers, params in layers_impact.items():
        print(f"  {layers:3d} -> 平均参数量: {params:8,.0f}")

    # 找出参数量最大和最小的配置
    print("\n3. 极值配置:")
    print("-" * 40)

    max_params_idx = df['trainable_params'].idxmax()
    min_params_idx = df['trainable_params'].idxmin()

    max_config = df.loc[max_params_idx]
    min_config = df.loc[min_params_idx]

    print("参数量最大的配置:")
    print(f"  数据集: {max_config['dataset']}")
    print(f"  wavelet_dim={max_config['wavelet_dim']}, rnn_dim={max_config['rnn_dim']}, "
          f"num_levels={max_config['num_levels']}, num_layers={max_config['num_layers']}")
    print(f"  参数量: {max_config['trainable_params']:,}")

    print("参数量最小的配置:")
    print(f"  数据集: {min_config['dataset']}")
    print(f"  wavelet_dim={min_config['wavelet_dim']}, rnn_dim={min_config['rnn_dim']}, "
          f"num_levels={min_config['num_levels']}, num_layers={min_config['num_layers']}")
    print(f"  参数量: {min_config['trainable_params']:,}")

def generate_summary_report(df):
    """生成汇总报告"""

    # 创建汇总统计
    summary_stats = []

    # 按数据集和参数组合生成汇总
    for dataset in df['dataset'].unique():
        dataset_df = df[df['dataset'] == dataset]

        # 按参数组合分组
        param_groups = dataset_df.groupby(['wavelet_dim', 'rnn_dim', 'num_levels', 'num_layers'])

        for (wavelet_dim, rnn_dim, num_levels, num_layers), group in param_groups:
            if len(group) > 0:  # 确保组不为空
                row = group.iloc[0]  # 取第一行（应该只有一行）
                summary_stats.append({
                    'dataset': dataset,
                    'wavelet_dim': wavelet_dim,
                    'rnn_dim': rnn_dim,
                    'num_levels': num_levels,
                    'num_layers': num_layers,
                    'sequence_length': row['sequence_length'],
                    'num_classes': row['num_classes'],
                    'trainable_params': row['trainable_params'],
                    'total_params': row['total_params']
                })

    # 转换为DataFrame
    summary_df = pd.DataFrame(summary_stats)

    # 保存汇总报告
    output_dir = 'parameter_analysis'
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    summary_file = os.path.join(output_dir, f'parameter_summary_{timestamp}.csv')
    summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
    print(f"汇总报告已保存到: {summary_file}")

    return summary_df, summary_file

def main():
    """主函数"""
    print("WNN-MRNN模型参数量计算工具")
    print("=" * 50)

    try:
        # 计算所有参数组合的参数量
        results = calculate_parameters_for_combinations()

        if not results:
            print("没有成功计算任何参数组合，请检查模型配置")
            return

        # 保存结果到文件
        df, csv_file, json_file = save_results_to_files(results)

        # 分析结果
        analyze_results(df)

        # 生成汇总报告
        summary_df, summary_file = generate_summary_report(df)

        print(f"\n总共计算了 {len(results)} 种参数组合")
        print(f"涵盖 {len(df['dataset'].unique())} 个数据集")
        print(f"参数量范围: {df['trainable_params'].min():,} - {df['trainable_params'].max():,}")

        print("\n文件输出:")
        print(f"- 详细结果: {csv_file}")
        print(f"- JSON格式: {json_file}")
        print(f"- 汇总报告: {summary_file}")

    except Exception as e:
        print(f"计算过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
