import numpy as np
import pickle
import torch
import os
import scipy.io as sio
import h5py
from torch.utils.data import Dataset, DataLoader
from sklearn.model_selection import train_test_split


def _normalize_rml2018_data(X, normalization_method='standard'):
    """
    对RML2018数据进行归一化处理的辅助函数

    参数:
        X: 输入数据，形状为 [n_samples, 2, signal_length]
        normalization_method: 归一化方法

    返回:
        归一化后的数据
    """
    if normalization_method == 'standard':
        # 标准化：零均值，单位方差
        # 分别对I和Q通道进行标准化
        X_normalized = np.zeros_like(X)
        for channel in range(X.shape[1]):  # 对每个通道(I/Q)
            channel_data = X[:, channel, :]
            mean = np.mean(channel_data)
            std = np.std(channel_data)
            if std > 1e-8:  # 避免除零
                X_normalized[:, channel, :] = (channel_data - mean) / std
            else:
                X_normalized[:, channel, :] = channel_data - mean
        return X_normalized

    elif normalization_method == 'minmax':
        # 最小-最大归一化：缩放到[-1, 1]
        X_normalized = np.zeros_like(X)
        for channel in range(X.shape[1]):  # 对每个通道(I/Q)
            channel_data = X[:, channel, :]
            min_val = np.min(channel_data)
            max_val = np.max(channel_data)
            if max_val > min_val:  # 避免除零
                X_normalized[:, channel, :] = 2 * (channel_data - min_val) / (max_val - min_val) - 1
            else:
                X_normalized[:, channel, :] = channel_data - min_val
        return X_normalized

    elif normalization_method == 'robust':
        # 鲁棒归一化：使用中位数和四分位距
        X_normalized = np.zeros_like(X)
        for channel in range(X.shape[1]):  # 对每个通道(I/Q)
            channel_data = X[:, channel, :]
            median = np.median(channel_data)
            q75, q25 = np.percentile(channel_data, [75, 25])
            iqr = q75 - q25
            if iqr > 1e-8:  # 避免除零
                X_normalized[:, channel, :] = (channel_data - median) / iqr
            else:
                X_normalized[:, channel, :] = channel_data - median
        return X_normalized

    elif normalization_method == 'per_sample':
        # 逐样本归一化：每个样本独立进行标准化
        X_normalized = np.zeros_like(X)
        for i in range(X.shape[0]):  # 对每个样本
            sample = X[i]  # 形状: [2, signal_length]
            mean = np.mean(sample)
            std = np.std(sample)
            if std > 1e-8:  # 避免除零
                X_normalized[i] = (sample - mean) / std
            else:
                X_normalized[i] = sample - mean
        return X_normalized

    else:
        raise ValueError(f"不支持的归一化方法: {normalization_method}")


class RML2016Dataset(Dataset):
    """
    RML2016.10a数据集的PyTorch数据集类
    
    该数据集包含不同调制类型和SNR级别的无线电信号I/Q样本
    """
    def __init__(self, X, y, snrs=None, device='cpu'):
        """
        初始化数据集
        
        参数:
            X: 信号数据，形状为 [n_samples, 2, signal_length]
            y: 调制类型标签，形状为 [n_samples]
            snrs: SNR值，形状为 [n_samples]，可选
            device: 将数据放在哪个设备上('cpu'或'cuda')
        """
        self.X = torch.tensor(X, dtype=torch.float32, device=device)
        self.y = torch.tensor(y, dtype=torch.long, device=device)
        if snrs is not None:
            self.snrs = torch.tensor(snrs, dtype=torch.float32, device=device)
        else:
            self.snrs = None
            
    def __len__(self):
        """
        返回数据集大小
        """
        return len(self.X)
    
    def __getitem__(self, idx):
        """
        获取单个样本
        
        参数:
            idx: 样本索引
            
        返回:
            (X, y, snr): 信号样本，标签和SNR值的元组
        """
        if self.snrs is not None:
            return self.X[idx], self.y[idx], self.snrs[idx]
        else:
            return self.X[idx], self.y[idx], torch.tensor(0.0)


class RML201801aDataset(Dataset):
    """
    RML2018.01a数据集的PyTorch数据集类

    该数据集包含24种调制类型和多个SNR级别的无线电信号I/Q样本
    数据存储在HDF5格式中，形状为32x32x2
    """
    def __init__(self, X, y, snrs=None, device='cpu', normalize=True, normalization_method='standard'):
        """
        初始化数据集

        参数:
            X: 信号数据，形状为 [n_samples, 2, signal_length]
            y: 调制类型标签，形状为 [n_samples]
            snrs: SNR值，形状为 [n_samples]，可选
            device: 将数据放在哪个设备上('cpu'或'cuda')
            normalize: 是否对数据进行归一化，默认为True
            normalization_method: 归一化方法，可选：
                - 'standard': 标准化 (零均值，单位方差)
                - 'minmax': 最小-最大归一化 (缩放到[0,1]或[-1,1])
                - 'robust': 鲁棒归一化 (使用中位数和四分位距)
                - 'per_sample': 逐样本归一化 (每个样本独立归一化)
        """
        self.normalize = normalize
        self.normalization_method = normalization_method

        # 转换为numpy数组以便进行归一化处理
        if isinstance(X, torch.Tensor):
            X_np = X.cpu().numpy()
        else:
            X_np = np.array(X, dtype=np.float32)

        # 如果需要归一化，则进行归一化处理
        if self.normalize:
            X_np = self._normalize_data(X_np)
            print(f"RML2018数据已使用 '{normalization_method}' 方法进行归一化")

        # 转换为PyTorch张量
        self.X = torch.tensor(X_np, dtype=torch.float32, device=device)
        self.y = torch.tensor(y, dtype=torch.long, device=device)
        if snrs is not None:
            self.snrs = torch.tensor(snrs, dtype=torch.float32, device=device)
        else:
            self.snrs = None

    def _normalize_data(self, X):
        """
        对数据进行归一化处理

        参数:
            X: 输入数据，形状为 [n_samples, 2, signal_length]

        返回:
            归一化后的数据
        """
        if self.normalization_method == 'standard':
            # 标准化：零均值，单位方差
            # 分别对I和Q通道进行标准化
            X_normalized = np.zeros_like(X)
            for channel in range(X.shape[1]):  # 对每个通道(I/Q)
                channel_data = X[:, channel, :]
                mean = np.mean(channel_data)
                std = np.std(channel_data)
                if std > 1e-8:  # 避免除零
                    X_normalized[:, channel, :] = (channel_data - mean) / std
                else:
                    X_normalized[:, channel, :] = channel_data - mean
            return X_normalized

        elif self.normalization_method == 'minmax':
            # 最小-最大归一化：缩放到[-1, 1]
            X_normalized = np.zeros_like(X)
            for channel in range(X.shape[1]):  # 对每个通道(I/Q)
                channel_data = X[:, channel, :]
                min_val = np.min(channel_data)
                max_val = np.max(channel_data)
                if max_val > min_val:  # 避免除零
                    X_normalized[:, channel, :] = 2 * (channel_data - min_val) / (max_val - min_val) - 1
                else:
                    X_normalized[:, channel, :] = channel_data - min_val
            return X_normalized

        elif self.normalization_method == 'robust':
            # 鲁棒归一化：使用中位数和四分位距
            X_normalized = np.zeros_like(X)
            for channel in range(X.shape[1]):  # 对每个通道(I/Q)
                channel_data = X[:, channel, :]
                median = np.median(channel_data)
                q75, q25 = np.percentile(channel_data, [75, 25])
                iqr = q75 - q25
                if iqr > 1e-8:  # 避免除零
                    X_normalized[:, channel, :] = (channel_data - median) / iqr
                else:
                    X_normalized[:, channel, :] = channel_data - median
            return X_normalized

        elif self.normalization_method == 'per_sample':
            # 逐样本归一化：每个样本独立进行标准化
            X_normalized = np.zeros_like(X)
            for i in range(X.shape[0]):  # 对每个样本
                sample = X[i]  # 形状: [2, signal_length]
                mean = np.mean(sample)
                std = np.std(sample)
                if std > 1e-8:  # 避免除零
                    X_normalized[i] = (sample - mean) / std
                else:
                    X_normalized[i] = sample - mean
            return X_normalized

        else:
            raise ValueError(f"不支持的归一化方法: {self.normalization_method}")

    def __len__(self):
        """
        返回数据集大小
        """
        return len(self.X)

    def __getitem__(self, idx):
        """
        获取单个样本

        参数:
            idx: 样本索引

        返回:
            (X, y, snr): 信号样本，标签和SNR值的元组
        """
        if self.snrs is not None:
            return self.X[idx], self.y[idx], self.snrs[idx]
        else:
            return self.X[idx], self.y[idx], torch.tensor(0.0)


class HisarModDataset(Dataset):
    def __init__(self, data_path, labels_path, snr_path, sequence_length=1024, is_training=True, device='cpu'):
        """
        初始化HisarMod数据集
        Args:
            data_path: 数据文件路径 (.mat 格式)
            labels_path: 标签文件路径 (.csv 格式)
            snr_path: SNR文件路径 (.csv 格式)
            sequence_length: 序列长度，如果与原始数据不同，将进行裁剪或填充
            is_training: 是否为训练模式
            device: 将数据放在哪个设备上('cpu'或'cuda')
        """
        self.sequence_length = sequence_length
        self.is_training = is_training
        self.data_path = data_path
        self.device = device
        
        # 提示正在加载数据集
        print(f"加载数据集: {os.path.basename(data_path)}")
        
        # 定义HisarMod的调制类型映射
        self.label_map = {
            0: "BPSK", 10: "QPSK", 20: "8PSK", 30: "16PSK", 40: "32PSK", 50: "64PSK",
            1: "4QAM", 11: "8QAM", 21: "16QAM", 31: "32QAM", 41: "64QAM", 
            51: "128QAM", 61: "256QAM", 2: "2FSK", 12: "4FSK", 22: "8FSK", 
            32: "16FSK", 3: "4PAM", 13: "8PAM", 23: "16PAM", 4: "AM-DSB", 
            14: "AM-DSB-SC", 24: "AM-USB", 34: "AM-LSB", 44: "FM", 54: "PM"
        }
        
        # 创建从原始标签值到连续索引的映射
        self.label_to_idx = {}
        sorted_labels = sorted(self.label_map.keys())
        for i, label in enumerate(sorted_labels):
            self.label_to_idx[label] = i
        
        # 创建从连续索引到调制类型名称的映射，方便后续使用
        self.idx_to_mod = {}
        for i, label in enumerate(sorted_labels):
            self.idx_to_mod[i] = self.label_map[label]
        
        # 从标签映射中提取调制类型列表
        self.mod_types = [self.label_map[k] for k in sorted_labels]
        
        # 首先尝试检查文件格式并决定最佳加载方法
        if not os.path.exists(data_path):
            raise FileNotFoundError(f"文件不存在: {data_path}")
            
        # 尝试先用scipy.io获取文件信息
        try:
            mat_info = sio.whosmat(data_path)
            
            # 确定使用哪种方法加载
            file_size = os.path.getsize(data_path) / (1024 * 1024)  # 大小(MB)
            
            # 如果文件较小，直接使用scipy.io加载
            if file_size < 1000:  # 小于1GB
                self._load_with_scipy(data_path, mat_info)
            else:
                # 对于大文件，使用部分加载
                try:
                    import h5py
                    self._load_with_h5py(data_path)
                except ImportError:
                    print("警告: h5py未安装，无法部分加载大文件。将尝试完整加载...")
                    self._load_with_scipy(data_path, mat_info)
                except Exception as e:
                    print(f"使用h5py加载失败: {e}，将尝试使用scipy.io")
                    self._load_with_scipy(data_path, mat_info)
        except Exception as e:
            print(f"检查MAT文件信息时出错: {e}")
            # 尝试直接使用scipy.io加载
            try:
                self._load_with_scipy(data_path)
            except Exception as e2:
                print(f"使用scipy.io加载失败: {e2}")
                raise ValueError(f"无法加载数据文件: {data_path}")
        
        # 加载标签和SNR
        try:
            self.orig_labels = np.loadtxt(labels_path, delimiter=',', dtype=np.int32)
            self.snrs = np.loadtxt(snr_path, delimiter=',', dtype=np.float32)
            
            # 将原始标签转换为连续索引标签
            self.labels = np.zeros_like(self.orig_labels)
            for i, label in enumerate(self.orig_labels):
                if label in self.label_to_idx:
                    self.labels[i] = self.label_to_idx[label]
                else:
                    print(f"警告: 未知标签值 {label}，将设为0")
                    self.labels[i] = 0
            
            # 检查长度是否匹配
            if len(self.labels) != self.num_samples or len(self.snrs) != self.num_samples:
                print(f"警告: 标签数量 {len(self.labels)} 或SNR数量 {len(self.snrs)} 与样本数量 {self.num_samples} 不匹配")
                
            # 检查标签是否在有效范围内
            unique_labels = np.unique(self.labels)
            if max(unique_labels) >= len(self.mod_types) or min(unique_labels) < 0:
                print(f"警告: 标签值范围 {min(unique_labels)}-{max(unique_labels)} 不在有效范围 0-{len(self.mod_types)-1}")
            
            # 转换为PyTorch张量
            self.labels = torch.from_numpy(self.labels).long()
            self.snrs = torch.from_numpy(self.snrs).float()
            
        except Exception as e:
            print(f"加载标签或SNR文件时出错: {e}")
            raise
        
        # 只打印关键信息
        print(f"数据集加载完成: {self.num_samples}样本, {len(np.unique(self.labels.numpy()))}类, {len(np.unique(self.snrs.numpy()))}种SNR值")
    
    def _load_with_scipy(self, data_path, mat_info=None):
        """使用scipy.io加载整个MAT文件"""
        # 查找数据变量名
        data_var = 'data'  # 默认变量名
        if mat_info is not None:
            # 检查mat_info中是否包含data变量
            data_var_found = False
            for name, shape, dtype in mat_info:
                if name == 'data':
                    data_var = name
                    data_var_found = True
                    break
            
            # 如果没有找到data变量，使用第一个变量
            if not data_var_found and len(mat_info) > 0:
                data_var = mat_info[0][0]
        
        # 加载完整数据
        mat_data = sio.loadmat(data_path)
        if data_var not in mat_data:
            # 尝试查找可用的变量
            available_vars = [k for k in mat_data.keys() if not k.startswith('__')]
            if available_vars:
                data_var = available_vars[0]
            else:
                raise ValueError(f"MAT文件中没有可用的数据变量")
                
        self.data = mat_data[data_var]
        
        # 确定数据布局
        if len(self.data.shape) == 3:
            if self.data.shape[1] == 2:
                # 形状为 (样本数, 2, 序列长度)
                self.num_samples = self.data.shape[0]
                self.num_channels = self.data.shape[1]
                self.data_length = self.data.shape[2]
            elif self.data.shape[0] == 2:
                # 形状可能是 (2, 样本数, 序列长度)
                self.data = np.transpose(self.data, (1, 0, 2))
                self.num_samples = self.data.shape[0]
                self.num_channels = self.data.shape[1]
                self.data_length = self.data.shape[2]
            else:
                raise ValueError(f"数据维度不符合要求: {self.data.shape}, 需要2个通道")
        else:
            raise ValueError(f"不支持的数据形状: {self.data.shape}, 期望3维数据")
    
    def _load_with_h5py(self, data_path):
        """使用h5py部分加载MAT文件"""
        import h5py
        
        self.h5_file = h5py.File(data_path, 'r')
        
        # 查找数据变量
        if 'data' in self.h5_file:
            self.data_ref = self.h5_file['data']
        elif 'data_save' in self.h5_file:
            self.data_ref = self.h5_file['data_save']
        else:
            possible_vars = list(self.h5_file.keys())
            if len(possible_vars) > 0:
                self.data_ref = self.h5_file[possible_vars[0]]
            else:
                raise ValueError("无法找到数据变量")
        
        # 获取数据形状
        self.data_shape = self.data_ref.shape
        
        # 确定数据布局
        if len(self.data_shape) == 3:
            if self.data_shape[1] == 2 and self.data_shape[0] > 10:
                # 形状为 (样本数, 2, 序列长度)
                self.transpose_needed = False
                self.num_samples = self.data_shape[0]
                self.num_channels = self.data_shape[1]
                self.data_length = self.data_shape[2]
            elif self.data_shape[1] == 2 and self.data_shape[2] > 10:
                # 形状为 (序列长度, 2, 样本数) - 需要转置
                self.transpose_needed = True
                self.num_samples = self.data_shape[2]
                self.num_channels = self.data_shape[1]
                self.data_length = self.data_shape[0]
            else:
                raise ValueError(f"数据维度不符合要求: {self.data_shape}")
        else:
            raise ValueError(f"不支持的数据形状: {self.data_shape}")
        
        # 标记数据加载模式
        self.use_h5py = True
    
    def __len__(self):
        """返回数据集大小"""
        return self.num_samples
    
    def __getitem__(self, idx):
        """获取单个样本"""
        try:
            if hasattr(self, 'use_h5py') and self.use_h5py:
                # 使用h5py按需加载
                if hasattr(self, 'transpose_needed') and self.transpose_needed:
                    # 形状 (序列长度, 2, 样本数) -> (2, 序列长度)
                    signal_data = self.data_ref[:, :, idx].T
                else:
                    # 形状 (样本数, 2, 序列长度) -> (2, 序列长度)
                    signal_data = self.data_ref[idx]
                
                # 转换为张量
                signal = torch.from_numpy(signal_data).float()
            else:
                # 使用预加载的数据
                signal_data = self.data[idx]
                signal = torch.from_numpy(signal_data).float()
            
            # 归一化信号
            mean = signal.mean()
            std = signal.std() + 1e-8
            signal = (signal - mean) / std
            
            # 将数据移到指定设备
            signal = signal.to(self.device)
            return signal, self.labels[idx].to(self.device), self.snrs[idx].to(self.device)
            
        except Exception as e:
            print(f"加载样本 {idx} 时出错: {e}")
            # 出错时返回零信号
            signal = torch.zeros((self.num_channels, self.data_length), device=self.device)
            return signal, self.labels[idx].to(self.device), self.snrs[idx].to(self.device)
    
    def __del__(self):
        """确保关闭文件"""
        if hasattr(self, 'h5_file'):
            try:
                self.h5_file.close()
                print("已关闭H5文件")
            except:
                pass


def load_rml_dataset(file_path, modulations, samples_per_key=None):
    """
    加载RML2016.10a数据集

    参数:
        file_path: 数据集文件路径(.pkl格式)
        modulations: 要使用的调制类型列表，例如 ['8PSK', 'AM-DSB', ...]
        samples_per_key: 每个(调制类型,SNR)对的样本数量，None表示使用所有样本

    返回:
        X: 形状为 [n_samples, 2, signal_length] 的信号数据
        labels: 形状为 [n_samples] 的标签
        snrs: 形状为 [n_samples] 的SNR值
    """
    print(f"从 {file_path} 加载数据集...")
    try:
        Xd = pickle.load(open(file_path, 'rb'), encoding='latin')
    except Exception as e:
        print(f"加载数据集时出错: {e}")
        raise

    # 创建调制类型映射
    modulation_map = {mod: idx for idx, mod in enumerate(modulations)}
    print(f"调制类型映射: {modulation_map}")

    # 提取唯一SNR值和调制类型
    snrs, mods = map(lambda j: sorted(list(set(map(lambda x: x[j], Xd.keys())))), [1, 0])
    print(f"SNR值范围: {min(snrs)} dB 到 {max(snrs)} dB")
    print(f"可用调制类型: {mods}")

    # 收集数据
    X, labels, snr_values = [], [], []

    for mod in mods:
        if mod not in modulations:
            print(f"跳过未请求的调制类型 {mod}")
            continue

        mod_samples = 0
        for snr in snrs:
            n_samples = Xd[(mod, snr)].shape[0]
            if samples_per_key:
                n_samples = min(n_samples, int(samples_per_key))

            for i in range(n_samples):
                X.append(Xd[(mod, snr)][i])
                labels.append(modulation_map[mod])
                snr_values.append(snr)

            mod_samples += n_samples

        print(f"调制类型 {mod} 加载了 {mod_samples} 个样本")

    X = np.array(X)
    labels = np.array(labels)
    snr_values = np.array(snr_values)

    print(f"数据集加载完成，共 {len(X)} 个样本，信号长度 {X.shape[-1]}")
    return X, labels, snr_values


def split_dataset(X, labels, snrs, train_ratio=0.7, seed=42, stratify_by_snr=False):
    """
    划分训练集和测试集

    参数:
        X: 信号数据
        labels: 标签
        snrs: SNR值
        train_ratio: 训练集比例
        seed: 随机种子
        stratify_by_snr: 是否按SNR分层采样，确保训练和测试集有相似的SNR分布

    返回:
        (X_train, y_train, snr_train), (X_test, y_test, snr_test): 训练集和测试集
    """
    np.random.seed(seed)
    n_samples = len(X)

    if stratify_by_snr:
        # 按SNR和调制类型分层
        unique_snrs = np.unique(snrs)
        unique_labels = np.unique(labels)

        train_indices = []
        test_indices = []

        for snr in unique_snrs:
            for label in unique_labels:
                mask = (snrs == snr) & (labels == label)
                indices = np.where(mask)[0]
                np.random.shuffle(indices)

                split_idx = int(len(indices) * train_ratio)
                train_indices.extend(indices[:split_idx])
                test_indices.extend(indices[split_idx:])
    else:
        # 随机分割
        indices = np.random.permutation(n_samples)
        train_size = int(train_ratio * n_samples)

        train_indices = indices[:train_size]
        test_indices = indices[train_size:]

    X_train = X[train_indices]
    y_train = labels[train_indices]
    snr_train = snrs[train_indices]

    X_test = X[test_indices]
    y_test = labels[test_indices]
    snr_test = snrs[test_indices]

    print(f"训练集: {len(X_train)} 样本")
    print(f"测试集: {len(X_test)} 样本")

    return (X_train, y_train, snr_train), (X_test, y_test, snr_test)


def load_rml201801a_dataset(file_path, modulations=None, use_all_snr=True, train_test_val_split=(0.7, 0.15, 0.15),
                           normalize=True, normalization_method='standard'):
    """
    加载RML2018.01a数据集

    参数:
        file_path: HDF5数据集文件路径
        modulations: 要使用的调制类型列表，None表示使用所有调制类型
        use_all_snr: 是否使用所有SNR级别的数据，True表示使用全部数据
        train_test_val_split: 训练/测试/验证集划分比例，元组格式(train, test, val)
        normalize: 是否对数据进行归一化，默认为True
        normalization_method: 归一化方法，可选：
            - 'standard': 标准化 (零均值，单位方差)
            - 'minmax': 最小-最大归一化 (缩放到[-1,1])
            - 'robust': 鲁棒归一化 (使用中位数和四分位距)
            - 'per_sample': 逐样本归一化 (每个样本独立归一化)

    返回:
        (X_train, y_train, snr_train), (X_test, y_test, snr_test), (X_val, y_val, snr_val): 训练、测试、验证集
    """
    print(f"从 {file_path} 加载RML2018.01a数据集...")

    # RML2018.01a数据集的24种调制类型
    base_modulation_classes = [
        'OOK', '4ASK', '8ASK', 'BPSK', 'QPSK', '8PSK', '16PSK', '32PSK',
        '16APSK', '32APSK', '64APSK', '128APSK', '16QAM', '32QAM', '64QAM',
        '128QAM', '256QAM', 'AM-SSB-WC', 'AM-SSB-SC', 'AM-DSB-WC', 'AM-DSB-SC',
        'FM', 'GMSK', 'OQPSK'
    ]

    # 如果没有指定调制类型，使用所有类型
    if modulations is None:
        selected_modulation_classes = base_modulation_classes
    else:
        selected_modulation_classes = modulations

    # 创建调制类型到索引的映射
    modulation_to_idx = {mod: idx for idx, mod in enumerate(selected_modulation_classes)}

    print(f"选择的调制类型 ({len(selected_modulation_classes)}): {selected_modulation_classes}")

    # 获取选择的调制类型在原始数据集中的索引
    selected_classes_id = [base_modulation_classes.index(cls) for cls in selected_modulation_classes]

    try:
        # 打开HDF5文件
        dataset_file = h5py.File(file_path, "r")

        # 检查数据集结构
        print(f"数据集键: {list(dataset_file.keys())}")

        # 获取数据和标签
        X_data = None
        y_data = None

        if use_all_snr:
            # 使用所有SNR级别的数据
            print("使用所有SNR级别的数据...")

            for id in selected_classes_id:
                # 每个调制类型有106496个样本，包含26个SNR级别，每个SNR级别4096个样本
                # 提取该调制类型的所有数据
                start_idx = 106496 * id
                end_idx = 106496 * (id + 1)

                X_slice = dataset_file['X'][start_idx:end_idx]
                y_slice = dataset_file['Y'][start_idx:end_idx]

                if X_data is not None:
                    X_data = np.concatenate([X_data, X_slice], axis=0)
                    y_data = np.concatenate([y_data, y_slice], axis=0)
                else:
                    X_data = X_slice
                    y_data = y_slice
        else:
            # 使用部分SNR级别的数据（与原始代码示例相同，使用最后4个SNR级别）
            print("使用部分SNR级别的数据（最后4个SNR级别）...")
            N_SNR = 4  # 从30 SNR到22 SNR

            for id in selected_classes_id:
                X_slice = dataset_file['X'][(106496*(id+1) - 4096*N_SNR):106496*(id+1)]
                y_slice = dataset_file['Y'][(106496*(id+1) - 4096*N_SNR):106496*(id+1)]

                if X_data is not None:
                    X_data = np.concatenate([X_data, X_slice], axis=0)
                    y_data = np.concatenate([y_data, y_slice], axis=0)
                else:
                    X_data = X_slice
                    y_data = y_slice

        # 关闭文件
        dataset_file.close()

        print(f"原始数据形状: X={X_data.shape}, Y={y_data.shape}")

        # 重新整形数据从32x32x2到2x1024格式以适应WNN-MRNN
        # 原始数据是32x32x2，我们需要将其转换为2x1024
        X_data = X_data.reshape(len(X_data), 32, 32, 2)

        # 将32x32x2重新整形为2x1024
        # 方法1：将32x32展平为1024，然后转置通道维度
        X_reshaped = np.zeros((len(X_data), 2, 1024), dtype=np.float32)
        for i in range(len(X_data)):
            # I通道 (第0个通道)
            X_reshaped[i, 0, :] = X_data[i, :, :, 0].flatten()
            # Q通道 (第1个通道)
            X_reshaped[i, 1, :] = X_data[i, :, :, 1].flatten()

        X_data = X_reshaped

        print(f"重新整形后数据形状: {X_data.shape}")

        # 对数据进行归一化处理（如果启用）
        if normalize:
            print(f"开始使用 '{normalization_method}' 方法对RML2018数据进行归一化...")
            X_data = _normalize_rml2018_data(X_data, normalization_method)
            print(f"RML2018数据归一化完成")

            # 打印归一化后的统计信息
            print(f"归一化后统计信息:")
            print(f"  I通道 - 均值: {np.mean(X_data[:, 0, :]):.6f}, 标准差: {np.std(X_data[:, 0, :]):.6f}")
            print(f"  Q通道 - 均值: {np.mean(X_data[:, 1, :]):.6f}, 标准差: {np.std(X_data[:, 1, :]):.6f}")
            print(f"  整体数据范围: [{np.min(X_data):.6f}, {np.max(X_data):.6f}]")

        # 处理标签数据
        # y_data是one-hot编码，需要转换为类别索引
        if len(y_data.shape) > 1 and y_data.shape[1] > 1:
            # 如果是one-hot编码，转换为类别索引
            y_labels = np.argmax(y_data, axis=1)
        else:
            y_labels = y_data.flatten()

        # 重新映射标签到连续的索引
        y_mapped = np.zeros_like(y_labels)
        for i, label in enumerate(y_labels):
            # 找到对应的调制类型
            if label < len(base_modulation_classes):
                mod_name = base_modulation_classes[label]
                if mod_name in modulation_to_idx:
                    y_mapped[i] = modulation_to_idx[mod_name]
                else:
                    print(f"警告: 调制类型 {mod_name} 不在选择列表中")

        # 生成SNR值（假设数据按SNR顺序排列）
        # RML2018.01a包含26个SNR级别，从-20dB到30dB，步长2dB
        if use_all_snr:
            snr_levels = np.arange(-20, 32, 2)  # -20到30，步长2
            samples_per_snr = 4096
        else:
            snr_levels = np.arange(22, 32, 2)  # 最后4个SNR级别：22, 24, 26, 28, 30
            samples_per_snr = 4096

        # 为每个样本分配SNR值
        snr_values = []
        for mod_idx in range(len(selected_modulation_classes)):
            for snr in snr_levels:
                snr_values.extend([snr] * samples_per_snr)

        snr_values = np.array(snr_values[:len(X_data)])  # 确保长度匹配

        print(f"数据集加载完成:")
        print(f"  样本数量: {len(X_data)}")
        print(f"  调制类型数量: {len(selected_modulation_classes)}")
        print(f"  SNR级别: {snr_levels}")
        print(f"  数据形状: {X_data.shape}")
        print(f"  标签范围: [{y_mapped.min()}, {y_mapped.max()}]")
        print(f"  SNR范围: [{snr_values.min()}, {snr_values.max()}]")

        # 划分数据集 - 使用分层采样确保每个(SNR, 调制方式)组合在各个集合中都有相同比例
        train_ratio, test_ratio, val_ratio = train_test_val_split

        print("开始分层划分数据集...")
        print(f"划分比例 - 训练集: {train_ratio:.1%}, 测试集: {test_ratio:.1%}, 验证集: {val_ratio:.1%}")

        # 使用分层采样函数
        def create_stratified_split(X, y, snr, train_ratio, test_ratio, val_ratio, seed=42):
            """创建按(SNR, 调制方式)分层的数据集划分"""
            np.random.seed(seed)

            # 获取唯一的SNR和标签值
            unique_snrs = np.unique(snr)
            unique_labels = np.unique(y)

            print(f"  唯一SNR值: {len(unique_snrs)}个 (范围: {unique_snrs.min()}dB 到 {unique_snrs.max()}dB)")
            print(f"  唯一调制类型: {len(unique_labels)}个")

            train_indices = []
            test_indices = []
            val_indices = []

            # 对每个SNR和标签组合进行分层采样
            for snr_level in unique_snrs:
                for label_level in unique_labels:
                    # 找到符合条件的索引
                    mask = (snr == snr_level) & (y == label_level)
                    indices = np.where(mask)[0]

                    if len(indices) == 0:
                        continue  # 跳过没有样本的组合

                    # 随机打乱
                    np.random.shuffle(indices)

                    # 计算各集合的样本数量
                    n_total = len(indices)
                    n_train = int(n_total * train_ratio)
                    n_test = int(n_total * test_ratio)
                    n_val = n_total - n_train - n_test  # 剩余的分配给验证集

                    # 分割索引
                    train_indices.extend(indices[:n_train])
                    test_indices.extend(indices[n_train:n_train + n_test])
                    val_indices.extend(indices[n_train + n_test:n_train + n_test + n_val])

            # 再次打乱各个集合的索引
            np.random.shuffle(train_indices)
            np.random.shuffle(test_indices)
            np.random.shuffle(val_indices)

            return train_indices, test_indices, val_indices

        # 执行分层划分
        train_indices, test_indices, val_indices = create_stratified_split(
            X_data, y_mapped, snr_values, train_ratio, test_ratio, val_ratio, seed=42
        )

        # 根据索引分割数据
        X_train = X_data[train_indices]
        y_train = y_mapped[train_indices]
        snr_train = snr_values[train_indices]

        X_test = X_data[test_indices]
        y_test = y_mapped[test_indices]
        snr_test = snr_values[test_indices]

        X_val = X_data[val_indices]
        y_val = y_mapped[val_indices]
        snr_val = snr_values[val_indices]

        print(f"数据集划分完成:")
        print(f"  训练集: {len(X_train)} 样本 ({len(X_train)/len(X_data)*100:.1f}%)")
        print(f"  测试集: {len(X_test)} 样本 ({len(X_test)/len(X_data)*100:.1f}%)")
        print(f"  验证集: {len(X_val)} 样本 ({len(X_val)/len(X_data)*100:.1f}%)")

        # 验证分层效果
        print("\n验证分层划分效果:")

        # 检查SNR分布
        train_snr_unique, train_snr_counts = np.unique(snr_train, return_counts=True)
        test_snr_unique, test_snr_counts = np.unique(snr_test, return_counts=True)
        val_snr_unique, val_snr_counts = np.unique(snr_val, return_counts=True)

        print(f"  训练集SNR分布: {len(train_snr_unique)}个SNR级别")
        print(f"  测试集SNR分布: {len(test_snr_unique)}个SNR级别")
        print(f"  验证集SNR分布: {len(val_snr_unique)}个SNR级别")

        # 检查调制类型分布
        train_mod_unique, train_mod_counts = np.unique(y_train, return_counts=True)
        test_mod_unique, test_mod_counts = np.unique(y_test, return_counts=True)
        val_mod_unique, val_mod_counts = np.unique(y_val, return_counts=True)

        print(f"  训练集调制类型分布: {len(train_mod_unique)}种调制类型")
        print(f"  测试集调制类型分布: {len(test_mod_unique)}种调制类型")
        print(f"  验证集调制类型分布: {len(val_mod_unique)}种调制类型")

        # 检查每个(SNR, 调制类型)组合的分布比例
        print("  验证各集合中(SNR, 调制类型)组合的比例一致性...")
        total_combinations = 0
        consistent_combinations = 0

        for snr_level in np.unique(snr_values):
            for mod_type in np.unique(y_mapped):
                # 计算原始数据中该组合的总数
                total_mask = (snr_values == snr_level) & (y_mapped == mod_type)
                total_count = np.sum(total_mask)

                if total_count == 0:
                    continue

                total_combinations += 1

                # 计算各集合中该组合的数量
                train_count = np.sum((snr_train == snr_level) & (y_train == mod_type))
                test_count = np.sum((snr_test == snr_level) & (y_test == mod_type))
                val_count = np.sum((snr_val == snr_level) & (y_val == mod_type))

                # 计算比例
                train_ratio_actual = train_count / total_count if total_count > 0 else 0
                test_ratio_actual = test_count / total_count if total_count > 0 else 0
                val_ratio_actual = val_count / total_count if total_count > 0 else 0

                # 检查比例是否接近预期（允许5%的误差）
                if (abs(train_ratio_actual - train_ratio) < 0.05 and
                    abs(test_ratio_actual - test_ratio) < 0.05 and
                    abs(val_ratio_actual - val_ratio) < 0.05):
                    consistent_combinations += 1

        consistency_rate = consistent_combinations / total_combinations * 100 if total_combinations > 0 else 0
        print(f"  分层一致性: {consistent_combinations}/{total_combinations} ({consistency_rate:.1f}%) 组合符合预期比例")

        return (X_train, y_train, snr_train), (X_test, y_test, snr_test), (X_val, y_val, snr_val)

    except Exception as e:
        print(f"加载RML2018.01a数据集时出错: {e}")
        raise


def get_hisar_data_loaders(config, num_workers=1, pin_memory=True, persist_workers=False):
    """创建Hisar数据集的数据加载器"""
    # 获取数据集特定的batch_size（如果有的话）
    dataset_type = config['data']['dataset_type']
    dataset_params = config['model']['dataset_specific_params'].get(dataset_type, {})
    batch_size = dataset_params.get('batch_size', config['training']['batch_size'])
    sequence_length = config['model']['sequence_length']
    # 修复设备选择逻辑，添加默认值处理
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    if 'device' in config.get('training', {}):
        device = config['training']['device']

    # 获取数据文件路径
    data_dir = config['data'].get('hisar_data_dir', 'data/HisarMod')
    train_file = config['data'].get('hisar_train_file', 'train.mat')
    test_file = config['data'].get('hisar_test_file', 'test.mat')

    # 构建完整的文件路径
    train_path = os.path.join(data_dir, train_file)
    test_path = os.path.join(data_dir, test_file)

    # 构建标签和SNR文件路径
    train_labels_path = os.path.join(data_dir, 'train_labels.csv')
    train_snr_path = os.path.join(data_dir, 'train_snr.csv')
    test_labels_path = os.path.join(data_dir, 'test_labels.csv')
    test_snr_path = os.path.join(data_dir, 'test_snr.csv')

    # 为了向后兼容，也支持从配置中直接读取完整路径
    train_path = config['data'].get('train_path', train_path)
    train_labels_path = config['data'].get('train_labels_path', train_labels_path)
    train_snr_path = config['data'].get('train_snr_path', train_snr_path)
    test_path = config['data'].get('test_path', test_path)
    test_labels_path = config['data'].get('test_labels_path', test_labels_path)
    test_snr_path = config['data'].get('test_snr_path', test_snr_path)

    # 打印文件路径信息
    print(f"训练数据文件: {train_path}")
    print(f"训练标签文件: {train_labels_path}")
    print(f"训练SNR文件: {train_snr_path}")
    print(f"测试数据文件: {test_path}")
    print(f"测试标签文件: {test_labels_path}")
    print(f"测试SNR文件: {test_snr_path}")

    # 加载HisarMod训练数据集
    print(f"加载训练集...")
    train_dataset = HisarModDataset(
        train_path,
        labels_path=train_labels_path,
        snr_path=train_snr_path,
        sequence_length=sequence_length,
        is_training=True,
        device='cpu'  # 初始加载在CPU上，在DataLoader中转移到GPU
    )

    # 创建分层索引
    def create_stratified_indices(dataset, train_ratio=0.8, seed=42):
        """创建分层采样的训练和验证索引"""
        rng = np.random.RandomState(seed)

        # 获取标签和SNR
        labels = dataset.labels.cpu().numpy()
        snrs = dataset.snrs.cpu().numpy()

        # 查找唯一值
        unique_snrs = np.unique(snrs)
        unique_labels = np.unique(labels)

        # 创建存储索引的列表
        train_indices = []
        val_indices = []

        # 对每个SNR和标签组合进行分层采样
        for snr in unique_snrs:
            for label in unique_labels:
                # 找到符合条件的索引
                indices = np.where((snrs == snr) & (labels == label))[0]

                if len(indices) == 0:
                    continue  # 跳过没有样本的组合

                # 随机打乱
                rng.shuffle(indices)

                # 分割
                n_train = int(len(indices) * train_ratio)
                train_indices.extend(indices[:n_train])
                val_indices.extend(indices[n_train:])

        # 再次打乱
        rng.shuffle(train_indices)
        rng.shuffle(val_indices)

        return train_indices, val_indices

    # 创建训练和验证索引
    train_indices, val_indices = create_stratified_indices(
        train_dataset,
        train_ratio=config['data'].get('train_ratio', 0.7),
        seed=config['training'].get('seed', 42)
    )

    print(f"数据集划分: 训练集 {len(train_indices)} 样本, 验证集 {len(val_indices)} 样本")

    # 创建数据子集
    from torch.utils.data import Subset
    train_subset = Subset(train_dataset, train_indices)
    val_subset = Subset(train_dataset, val_indices)

    # 优化配置参数
    prefetch_factor = config['training'].get('prefetch_factor', 2)

    # 对于大型数据集，禁用persistent_workers以减少内存使用
    persistent_workers = config['training'].get('persistent_workers', persist_workers)
    if torch.cuda.is_available() and persistent_workers and num_workers > 1:
        persistent_workers = True
    else:
        persistent_workers = False

    # 创建数据加载器
    train_loader = DataLoader(
        train_subset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory,
        prefetch_factor=prefetch_factor,
        persistent_workers=persistent_workers
    )

    val_loader = DataLoader(
        val_subset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory,
        prefetch_factor=prefetch_factor,
        persistent_workers=persistent_workers
    )

    # 加载测试集
    print(f"加载测试集...")
    test_dataset = HisarModDataset(
        test_path,
        labels_path=test_labels_path,
        snr_path=test_snr_path,
        sequence_length=sequence_length,
        is_training=False,
        device='cpu'
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory,
        prefetch_factor=prefetch_factor,
        persistent_workers=persistent_workers
    )

    print(f"数据加载完成，批次大小: {batch_size}")
    return train_loader, val_loader, test_loader


def get_torchsig_data_loaders(config, num_workers=1, pin_memory=True, persist_workers=False):
    """创建TorchSig数据集的数据加载器，支持不同序列长度(1024/2048/4096)"""
    # 获取数据集特定的batch_size（如果有的话）
    dataset_type = config['data']['dataset_type']
    dataset_params = config['model']['dataset_specific_params'].get(dataset_type, {})
    batch_size = dataset_params.get('batch_size', config['training']['batch_size'])
    sequence_length = config['model']['sequence_length']
    dataset_type = config['data'].get('dataset_type', 'torchsig1024')
    # 修复设备选择逻辑，添加默认值处理
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    if 'device' in config.get('training', {}):
        device = config['training']['device']

    # 从数据集类型中提取序列长度
    if 'torchsig' in dataset_type:
        try:
            seq_length = int(dataset_type.replace('torchsig', ''))
            if seq_length not in [1024, 2048, 4096]:
                print(f"警告: 非标准序列长度 {seq_length}，确保数据与此匹配")
            # 更新配置中的序列长度
            config['model']['sequence_length'] = seq_length
            sequence_length = seq_length
        except ValueError:
            print(f"警告: 无法从数据集类型 {dataset_type} 提取序列长度，使用默认值 {sequence_length}")

    print(f"使用TorchSig数据集，序列长度: {sequence_length}")

    # 根据数据集类型构造文件路径
    # 默认路径格式为 data/torchsig{seq_length}/train_data.mat
    base_dir = f"data/{dataset_type}"

    # 获取数据文件路径
    train_path = config['data'].get(f'{dataset_type}_train_path', f'{base_dir}/train_data.mat')
    train_labels_path = config['data'].get(f'{dataset_type}_train_labels_path', f'{base_dir}/train_labels.csv')
    train_snr_path = config['data'].get(f'{dataset_type}_train_snr_path', f'{base_dir}/train_snr.csv')

    test_path = config['data'].get(f'{dataset_type}_test_path', f'{base_dir}/test_data.mat')
    test_labels_path = config['data'].get(f'{dataset_type}_test_labels_path', f'{base_dir}/test_labels.csv')
    test_snr_path = config['data'].get(f'{dataset_type}_test_snr_path', f'{base_dir}/test_snr.csv')

    # 打印文件路径信息
    print(f"训练数据文件: {train_path}")
    print(f"训练标签文件: {train_labels_path}")
    print(f"训练SNR文件: {train_snr_path}")
    print(f"测试数据文件: {test_path}")
    print(f"测试标签文件: {test_labels_path}")
    print(f"测试SNR文件: {test_snr_path}")

    # 创建TorchSig数据集专用的HisarModDataset派生类
    class TorchSigDataset(HisarModDataset):
        def __init__(self, data_path, labels_path, snr_path, sequence_length=1024, is_training=True, device='cpu'):
            """初始化TorchSig数据集，重载标签映射"""
            self.sequence_length = sequence_length
            self.is_training = is_training
            self.data_path = data_path
            self.device = device

            # 提示正在加载数据集
            print(f"加载TorchSig数据集: {os.path.basename(data_path)}, 序列长度: {sequence_length}")

            # TorchSig数据集的调制类型映射 - 25种调制类型
            self.label_map = {
                0: "BPSK", 10: "QPSK", 20: "8PSK", 30: "16PSK", 40: "32PSK", 50: "64PSK",
                21: "16QAM", 31: "32QAM", 41: "64QAM", 61: "256QAM",
                2: "2FSK", 12: "4FSK", 22: "8FSK", 32: "16FSK",
                3: "4ASK", 13: "8ASK", 23: "16ASK", 33: "32ASK", 43: "64ASK",
                4: "AM-DSB", 14: "AM-DSB-SC", 24: "AM-USB", 34: "AM-LSB",
                44: "FM",
                64: "OOK"
            }

            # 创建从原始标签值到连续索引的映射
            self.label_to_idx = {}
            sorted_labels = sorted(self.label_map.keys())
            for i, label in enumerate(sorted_labels):
                self.label_to_idx[label] = i

            # 创建从连续索引到调制类型名称的映射，方便后续使用
            self.idx_to_mod = {}
            for i, label in enumerate(sorted_labels):
                self.idx_to_mod[i] = self.label_map[label]

            # 从标签映射中提取调制类型列表
            self.mod_types = [self.label_map[k] for k in sorted_labels]

            # 调用父类方法加载数据
            self._load_data(data_path, labels_path, snr_path)

        def _load_data(self, data_path, labels_path, snr_path):
            """加载数据的方法，复制自HisarModDataset的初始化部分"""
            # 首先尝试检查文件格式并决定最佳加载方法
            if not os.path.exists(data_path):
                raise FileNotFoundError(f"文件不存在: {data_path}")

            # 检查文件扩展名
            file_ext = os.path.splitext(data_path)[1].lower()

            # 根据文件扩展名选择不同的加载方式
            if file_ext == '.mat':
                self._load_mat_file(data_path)
            elif file_ext == '.npy':
                self._load_numpy_file(data_path)
            elif file_ext == '.npz':
                self._load_npz_file(data_path)
            else:
                raise ValueError(f"不支持的文件格式: {file_ext}")

            # 加载标签和SNR
            try:
                self.orig_labels = np.loadtxt(labels_path, delimiter=',', dtype=np.int32)
                self.snrs = np.loadtxt(snr_path, delimiter=',', dtype=np.float32)

                # 将原始标签转换为连续索引标签
                self.labels = np.zeros_like(self.orig_labels)
                for i, label in enumerate(self.orig_labels):
                    if label in self.label_to_idx:
                        self.labels[i] = self.label_to_idx[label]
                    else:
                        print(f"警告: 未知标签值 {label}，将设为0")
                        self.labels[i] = 0

                # 检查长度是否匹配
                if len(self.labels) != self.num_samples or len(self.snrs) != self.num_samples:
                    print(f"警告: 标签数量 {len(self.labels)} 或SNR数量 {len(self.snrs)} 与样本数量 {self.num_samples} 不匹配")

                # 检查标签是否在有效范围内
                unique_labels = np.unique(self.labels)
                if max(unique_labels) >= len(self.mod_types) or min(unique_labels) < 0:
                    print(f"警告: 标签值范围 {min(unique_labels)}-{max(unique_labels)} 不在有效范围 0-{len(self.mod_types)-1}")

                # 转换为PyTorch张量
                self.labels = torch.from_numpy(self.labels).long()
                self.snrs = torch.from_numpy(self.snrs).float()

            except Exception as e:
                print(f"加载标签或SNR文件时出错: {e}")
                raise

            # 只打印关键信息
            print(f"TorchSig数据集加载完成: {self.num_samples}样本, {len(np.unique(self.labels.numpy()))}类, {len(np.unique(self.snrs.numpy()))}种SNR值")

        def _load_mat_file(self, data_path):
            """尝试多种方式加载MAT文件"""
            print(f"尝试加载MAT文件: {data_path}")

            success = False
            error_messages = []

            # 方法1: 使用scipy.io直接加载
            try:
                import scipy.io as sio
                mat_data = sio.loadmat(data_path)
                # 查找可能的数据变量名
                data_vars = [k for k in mat_data.keys() if not k.startswith('__')]
                if data_vars:
                    data_var = data_vars[0]
                    self.data = mat_data[data_var]
                    success = self._validate_and_format_data()
                    if success:
                        print(f"使用scipy.io成功加载MAT文件，数据变量: {data_var}")
                        return
                else:
                    error_messages.append("MAT文件中没有找到数据变量")
            except Exception as e:
                error_messages.append(f"使用scipy.io加载失败: {str(e)}")

            # 方法2: 使用h5py加载
            try:
                import h5py
                with h5py.File(data_path, 'r') as f:
                    # 查找可能的数据变量
                    if len(f.keys()) > 0:
                        data_var = list(f.keys())[0]
                        # h5py加载的数据需要转置处理
                        self.data = np.array(f[data_var])
                        success = self._validate_and_format_data()
                        if success:
                            print(f"使用h5py成功加载MAT文件，数据变量: {data_var}")
                            return
                    else:
                        error_messages.append("H5py未找到数据变量")
            except ImportError:
                error_messages.append("h5py模块未安装，无法使用此方法")
            except Exception as e:
                error_messages.append(f"使用h5py加载失败: {str(e)}")

            # 如果所有方法都失败，则抛出异常
            if not success:
                error_msg = "无法加载MAT文件，尝试了以下方法:\n"
                for i, msg in enumerate(error_messages):
                    error_msg += f"方法{i+1}: {msg}\n"
                error_msg += "\n请检查文件格式或尝试转换为NPY格式。"
                raise ValueError(error_msg)

        def _load_numpy_file(self, data_path):
            """加载NumPy .npy文件"""
            try:
                self.data = np.load(data_path)
                success = self._validate_and_format_data()
                if not success:
                    raise ValueError("加载的NumPy数据格式不正确")
            except Exception as e:
                raise ValueError(f"加载NumPy文件失败: {e}")

        def _load_npz_file(self, data_path):
            """加载NumPy .npz文件"""
            try:
                npz_data = np.load(data_path)
                # 查找可能的数据数组
                if len(npz_data.files) > 0:
                    # 使用第一个数组
                    data_var = npz_data.files[0]
                    self.data = npz_data[data_var]
                    success = self._validate_and_format_data()
                    if not success:
                        raise ValueError("加载的NPZ数据格式不正确")
                else:
                    raise ValueError("NPZ文件不包含数据数组")
            except Exception as e:
                raise ValueError(f"加载NPZ文件失败: {e}")

        def _validate_and_format_data(self):
            """验证和格式化加载的数据"""
            # 检查数据是否存在
            if self.data is None:
                print("警告: 加载的数据为None")
                return False

            # 输出数据形状以辅助调试
            print(f"加载的数据形状: {self.data.shape}")

            # 处理不同的数据布局
            if len(self.data.shape) == 3:
                # 找出哪一维是通道维
                if self.data.shape[1] == 2:
                    # 形状为 (样本数, 2, 序列长度)
                    self.num_samples = self.data.shape[0]
                    self.num_channels = self.data.shape[1]
                    self.data_length = self.data.shape[2]
                elif self.data.shape[0] == 2:
                    # 形状为 (2, 样本数, 序列长度)
                    self.data = np.transpose(self.data, (1, 0, 2))
                    self.num_samples = self.data.shape[0]
                    self.num_channels = self.data.shape[1]
                    self.data_length = self.data.shape[2]
                elif self.data.shape[2] == 2:
                    # 形状为 (样本数, 序列长度, 2)
                    self.data = np.transpose(self.data, (0, 2, 1))
                    self.num_samples = self.data.shape[0]
                    self.num_channels = self.data.shape[1]
                    self.data_length = self.data.shape[2]
                else:
                    print(f"警告: 数据形状不符合预期: {self.data.shape}")
                    return False
            elif len(self.data.shape) == 2:
                # 可能是 (样本数, 序列长度*2) 或 (2*序列长度, 样本数)
                if self.data.shape[0] > self.data.shape[1]:
                    # 可能是 (样本数, 序列长度*2)
                    self.num_samples = self.data.shape[0]
                    seq_len = self.data.shape[1] // 2
                    # 转换为(样本数, 2, 序列长度)格式
                    self.data = np.stack([
                        self.data[:, :seq_len],  # I
                        self.data[:, seq_len:]   # Q
                    ], axis=1)
                    self.num_channels = 2
                    self.data_length = seq_len
                else:
                    # 可能是 (2*序列长度, 样本数) 需要转置
                    self.data = np.transpose(self.data)  # 变为 (样本数, 2*序列长度)
                    self.num_samples = self.data.shape[0]
                    seq_len = self.data.shape[1] // 2
                    # 转换为(样本数, 2, 序列长度)格式
                    self.data = np.stack([
                        self.data[:, :seq_len],  # I
                        self.data[:, seq_len:]   # Q
                    ], axis=1)
                    self.num_channels = 2
                    self.data_length = seq_len
            else:
                print(f"警告: 不支持的数据维度: {len(self.data.shape)}")
                return False

            # 检查IQ通道维度是否正确
            if self.num_channels != 2:
                print(f"警告: 通道数量 {self.num_channels} 不等于2(I/Q)")
                return False

            # 检查序列长度是否与预期一致
            if self.data_length != self.sequence_length:
                print(f"警告: 数据序列长度 {self.data_length} 与预期 {self.sequence_length} 不一致")
                # 如果长度不匹配，可以选择调整/裁剪数据以匹配预期长度
                if self.data_length > self.sequence_length:
                    # 如果数据长度大于预期，裁剪数据
                    print(f"裁剪数据到长度 {self.sequence_length}")
                    self.data = self.data[:, :, :self.sequence_length]
                    self.data_length = self.sequence_length
                else:
                    # 如果数据长度小于预期，通过零填充扩展数据
                    print(f"通过零填充扩展数据到长度 {self.sequence_length}")
                    pad_width = self.sequence_length - self.data_length
                    padded_data = np.zeros((self.num_samples, self.num_channels, self.sequence_length), dtype=self.data.dtype)
                    padded_data[:, :, :self.data_length] = self.data
                    self.data = padded_data
                    self.data_length = self.sequence_length

            # 最后将数据转换为numpy数组，确保类型正确
            self.data = np.array(self.data, dtype=np.float32)

            # 返回成功
            return True

    # 加载TorchSig训练数据集
    print(f"加载训练集...")
    train_dataset = TorchSigDataset(
        train_path,
        labels_path=train_labels_path,
        snr_path=train_snr_path,
        sequence_length=sequence_length,
        is_training=True,
        device='cpu'  # 初始加载在CPU上，在DataLoader中转移到GPU
    )

    # 创建分层索引
    def create_stratified_indices(dataset, train_ratio=0.8, seed=42):
        """创建分层采样的训练和验证索引"""
        rng = np.random.RandomState(seed)

        # 获取标签和SNR
        labels = dataset.labels.cpu().numpy()
        snrs = dataset.snrs.cpu().numpy()

        # 查找唯一值
        unique_snrs = np.unique(snrs)
        unique_labels = np.unique(labels)

        # 创建存储索引的列表
        train_indices = []
        val_indices = []

        # 对每个SNR和标签组合进行分层采样
        for snr in unique_snrs:
            for label in unique_labels:
                # 找到符合条件的索引
                indices = np.where((snrs == snr) & (labels == label))[0]

                if len(indices) == 0:
                    continue  # 跳过没有样本的组合

                # 随机打乱
                rng.shuffle(indices)

                # 分割
                n_train = int(len(indices) * train_ratio)
                train_indices.extend(indices[:n_train])
                val_indices.extend(indices[n_train:])

        # 再次打乱
        rng.shuffle(train_indices)
        rng.shuffle(val_indices)

        return train_indices, val_indices

    # 创建训练和验证索引
    train_indices, val_indices = create_stratified_indices(
        train_dataset,
        train_ratio=config['data'].get('train_ratio', 0.7),
        seed=config['training'].get('seed', 42)
    )

    print(f"数据集划分: 训练集 {len(train_indices)} 样本, 验证集 {len(val_indices)} 样本")

    # 创建数据子集
    from torch.utils.data import Subset
    train_subset = Subset(train_dataset, train_indices)
    val_subset = Subset(train_dataset, val_indices)

    # 优化配置参数
    prefetch_factor = config['training'].get('prefetch_factor', 2)

    # 对于大型数据集，禁用persistent_workers以减少内存使用
    persistent_workers = config['training'].get('persistent_workers', persist_workers)
    if torch.cuda.is_available() and persistent_workers and num_workers > 1:
        persistent_workers = True
    else:
        persistent_workers = False

    # 创建数据加载器
    train_loader = DataLoader(
        train_subset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=pin_memory,
        prefetch_factor=prefetch_factor,
        persistent_workers=persistent_workers
    )

    val_loader = DataLoader(
        val_subset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory,
        prefetch_factor=prefetch_factor,
        persistent_workers=persistent_workers
    )

    # 加载测试集
    print(f"加载测试集...")
    test_dataset = TorchSigDataset(
        test_path,
        labels_path=test_labels_path,
        snr_path=test_snr_path,
        sequence_length=sequence_length,
        is_training=False,
        device='cpu'
    )

    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=pin_memory,
        prefetch_factor=prefetch_factor,
        persistent_workers=persistent_workers
    )

    print(f"数据加载完成，批次大小: {batch_size}")
    return train_loader, val_loader, test_loader


def get_rml201801a_data_loaders(config, num_workers=1, pin_memory=True, persist_workers=False):
    """创建RML2018.01a数据集的数据加载器"""
    # 获取数据集特定的batch_size（如果有的话）
    dataset_type = config['data']['dataset_type']
    dataset_params = config['model']['dataset_specific_params'].get(dataset_type, {})
    batch_size = dataset_params.get('batch_size', config['training']['batch_size'])

    # 获取数据文件路径
    data_file = config['data'].get('rml201801a_file_path', 'data/GOLD_XYZ_OSC.0001_1024.hdf5')

    # 获取调制类型配置
    modulations = config['data'].get('rml201801a_modulations', None)  # None表示使用所有调制类型
    use_all_snr = config['data'].get('rml201801a_use_all_snr', True)  # 是否使用所有SNR级别

    # 获取数据集划分比例
    train_ratio = config['data'].get('train_ratio', 0.7)
    test_ratio = config['data'].get('test_ratio', 0.15)
    val_ratio = config['data'].get('val_ratio', 0.15)

    # 获取大内存优化参数
    preload_to_memory = config['training'].get('preload_to_memory', False)

    # 获取归一化参数
    normalize = config['data'].get('rml201801a_normalize', True)
    normalization_method = config['data'].get('rml201801a_normalization_method', 'standard')

    print(f"RML2018.01a数据集配置:")
    print(f"  数据文件: {data_file}")
    print(f"  使用所有SNR: {use_all_snr}")
    print(f"  数据集划分: 训练{train_ratio*100:.0f}%, 测试{test_ratio*100:.0f}%, 验证{val_ratio*100:.0f}%")
    print(f"  预加载到内存: {preload_to_memory}")
    print(f"  数据归一化: {normalize}")
    if normalize:
        print(f"  归一化方法: {normalization_method}")
    if modulations:
        print(f"  选择的调制类型: {modulations}")
    else:
        print(f"  使用所有调制类型")

    # 加载数据集
    (X_train, y_train, snr_train), (X_test, y_test, snr_test), (X_val, y_val, snr_val) = load_rml201801a_dataset(
        file_path=data_file,
        modulations=modulations,
        use_all_snr=use_all_snr,
        train_test_val_split=(train_ratio, test_ratio, val_ratio),
        normalize=normalize,
        normalization_method=normalization_method
    )

    # 根据配置选择设备
    device = 'cpu'
    if preload_to_memory and torch.cuda.is_available():
        # 检查GPU内存是否足够
        gpu_memory = torch.cuda.get_device_properties(0).total_memory
        estimated_memory = X_train.nbytes + X_val.nbytes + X_test.nbytes
        if estimated_memory < gpu_memory * 0.5:  # 使用不超过50%的GPU内存
            device = 'cuda'
            print(f"  数据将预加载到GPU内存 (估计使用 {estimated_memory/1024**3:.1f} GB)")
        else:
            print(f"  GPU内存不足，数据保持在CPU内存")

    # 创建数据集（注意：数据已经在load_rml201801a_dataset中进行了归一化，这里不需要再次归一化）
    train_dataset = RML201801aDataset(X_train, y_train, snr_train, device=device, normalize=False)
    val_dataset = RML201801aDataset(X_val, y_val, snr_val, device=device, normalize=False)
    test_dataset = RML201801aDataset(X_test, y_test, snr_test, device=device, normalize=False)

    # 优化配置参数
    prefetch_factor = config['training'].get('prefetch_factor', 2)
    multiprocessing_context = config['training'].get('multiprocessing_context', None)
    drop_last = config['training'].get('drop_last', False)

    # 对于大型数据集，启用persistent_workers以提高性能
    persistent_workers = config['training'].get('persistent_workers', persist_workers)
    if num_workers > 0:
        persistent_workers = True
    else:
        persistent_workers = False

    # 如果数据已经在GPU上，减少num_workers以避免不必要的数据传输
    if device == 'cuda':
        num_workers = min(num_workers, 2)
        pin_memory = False  # 数据已经在GPU上，不需要pin_memory
        print(f"  数据在GPU上，调整num_workers为{num_workers}，禁用pin_memory")

    # 创建数据加载器参数
    loader_kwargs = {
        'batch_size': batch_size,
        'num_workers': num_workers,
        'pin_memory': pin_memory,
        'persistent_workers': persistent_workers,
        'drop_last': drop_last
    }

    # 添加可选参数
    if num_workers > 0:
        loader_kwargs['prefetch_factor'] = prefetch_factor
    if multiprocessing_context and num_workers > 0:
        loader_kwargs['multiprocessing_context'] = multiprocessing_context

    # 创建数据加载器
    train_loader = DataLoader(
        train_dataset,
        shuffle=True,
        **loader_kwargs
    )

    val_loader = DataLoader(
        val_dataset,
        shuffle=False,
        **loader_kwargs
    )

    test_loader = DataLoader(
        test_dataset,
        shuffle=False,
        **loader_kwargs
    )

    print(f"RML2018.01a数据加载完成，批次大小: {batch_size}")
    print(f"  数据加载器配置: num_workers={num_workers}, prefetch_factor={prefetch_factor}")
    return train_loader, val_loader, test_loader
