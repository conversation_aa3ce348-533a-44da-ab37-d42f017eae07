import torch  # 导入PyTorch库，这是一个用于深度学习的Python库
import torch.nn as nn  # 导入神经网络模块，包含了各种神经网络层的定义
import torch.nn.functional as F  # 导入函数式接口，提供了各种激活函数和其他操作


class MMRNNCell(nn.Module):
    """
    标准LSTM单元 - 替换原来的Mamba RNN单元

    这个类使用标准的LSTM来处理拼接后的小波分量序列。
    """
    def __init__(self,
                 hidden_dim,  # 隐藏层维度，特征向量的大小
                 num_layers=1,  # LSTM的真正层数
                 drop=0.,  # 普通dropout率
                 **kwargs):  # 其他可能的参数（忽略Mamba相关参数）
        """
        初始化方法 - 创建标准LSTM所需的所有组件
        """
        super(MMRNNCell, self).__init__()  # 调用父类nn.Module的初始化方法

        # 使用标准LSTM
        self.lstm = nn.LSTM(
            input_size=hidden_dim,
            hidden_size=hidden_dim,
            num_layers=num_layers,  # 使用num_layers作为LSTM的真正层数
            batch_first=True,  # 输入格式为[batch, seq, feature]
            dropout=drop if num_layers > 1 else 0,  # 只有多层时才使用dropout
            bidirectional=False  # 单向LSTM
        )

        # 存储隐藏层维度
        self.hidden_dim = hidden_dim



    def forward(self, x):
        """
        前向传播函数 - 使用标准LSTM处理拼接后的小波分量序列

        参数:
            x: 拼接后的输入序列，形状为[B, S, C]，B是批次大小，S是序列长度，C是特征维度

        返回:
            output: LSTM输出，形状为[B, S, C]
        """
        # 直接通过LSTM处理拼接后的序列
        # x: [B, S, C] -> output: [B, S, C]
        output, _ = self.lstm(x)

        return output


class MMRNNClassifier(nn.Module):
    """基于LSTM的分类网络 - 用于序列分类任务

    这个网络将小波分解得到的多个分量拼接后送入LSTM进行处理。

    工作流程:
    1. 将各分量的特征投影到隐藏维度
    2. 将所有分量拼接成一个长序列
    3. 通过LSTM处理拼接后的序列
    4. 通过分类头得到最终预测
    """
    def __init__(
        self,
        input_dim: int,  # 输入特征维度，wavelet_dim
        hidden_dim: int,  # 隐藏层维度，rnn_dim
        num_components: int = 4,  # 频率分量数量，等于num_levels+1
        num_layers: int = 2,  # LSTM的真正层数
        num_classes: int = 10,  # 分类类别数
        dropout: float = 0.1,  # dropout比率
        **kwargs  # 忽略所有Mamba相关参数
    ):
        """
        初始化方法 - 创建MMRNNClassifier所需的所有组件
        """
        super().__init__()  # 调用父类nn.Module的初始化方法
        self.hidden_dim = hidden_dim  # 存储隐藏层维度
        self.num_layers = num_layers  # 存储层数
        self.num_components = num_components  # 存储分量数量

        # 输入特征投影层 - 将原始特征映射到隐藏维度
        # 这一层将每个分量的特征从input_dim(wavelet_dim)维映射到hidden_dim(rnn_dim)维
        self.input_proj = nn.Linear(input_dim, hidden_dim)

        # 创建LSTM处理拼接后的序列
        self.lstm = MMRNNCell(
            hidden_dim=hidden_dim,  # 设置隐藏层维度
            num_layers=num_layers,  # LSTM的真正层数
            drop=dropout  # dropout参数
        )

        # 分类头 - 将最终隐藏状态转换为类别预测
        # 使用Sequential容器组合多个层
        self.classifier = nn.Sequential(
            nn.LayerNorm(hidden_dim),  # 先对特征进行归一化
            nn.Linear(hidden_dim, num_classes)  # 然后映射到类别数量大小的输出
        )

    def forward(self, components_list, component_lengths=None):
        """
        前向传播函数 - 拼接小波分量后送入LSTM进行分类预测

        工作流程:
        1. 将各分量的特征投影到隐藏维度
        2. 将所有分量拼接成一个长序列
        3. 通过LSTM处理拼接后的序列
        4. 通过分类头得到最终预测

        参数:
            components_list: 分量列表，每个分量形状为[B, S_i, W]，其中S_i是各分量的序列长度
            component_lengths: 各分量的序列长度列表（可选，用于验证）

        返回:
            logits: 分类预测结果，形状为[B, num_classes]
        """
        # 兼容旧的调用方式
        if isinstance(components_list, torch.Tensor):
            # 如果传入的是张量，按照旧的方式处理
            return self._forward_legacy(components_list)

        # 新的分量列表处理方式
        projected_components = []  # 存储投影后的分量

        # 逐分量进行特征投影
        for component in components_list:
            # component形状为[B, S_c, W]，其中S_c是当前分量的序列长度，W是wavelet_dim
            # 将当前分量的特征投影到隐藏维度
            # [B, S_c, W] -> [B, S_c, hidden_dim]
            component_proj = self.input_proj(component)
            projected_components.append(component_proj)

        # 将所有分量在序列维度上拼接
        # 拼接后形状为[B, S_total, hidden_dim]，其中S_total = S_1 + S_2 + ... + S_n
        concatenated_sequence = torch.cat(projected_components, dim=1)

        # 通过LSTM处理拼接后的序列
        # [B, S_total, hidden_dim] -> [B, S_total, hidden_dim]
        lstm_output = self.lstm(concatenated_sequence)

        # 将特征在序列维度上平均池化
        # [B, S_total, hidden_dim] -> [B, hidden_dim]
        pooled_output = torch.mean(lstm_output, dim=1)

        # 分类预测 - 通过分类头得到最终预测
        # [B, hidden_dim] -> [B, num_classes]
        logits = self.classifier(pooled_output)

        return logits

    def _forward_legacy(self, x):
        """
        兼容旧版本的前向传播方法

        参数:
            x: 输入序列，形状为[B, C*S, W]

        返回:
            logits: 分类预测结果，形状为[B, num_classes]
        """
        B, CS, W = x.shape  # 获取输入的批次大小(B)和总序列长度(CS = C*S)及特征维度(W)

        # 先用输入特征投影层处理
        # [B, CS, W] -> [B, CS, hidden_dim]
        x_proj = self.input_proj(x)

        # 直接将拼接后的序列送入LSTM
        # [B, CS, hidden_dim] -> [B, CS, hidden_dim]
        lstm_output = self.lstm(x_proj)

        # 将特征在序列维度上平均池化
        # [B, CS, hidden_dim] -> [B, hidden_dim]
        pooled_output = torch.mean(lstm_output, dim=1)

        # 分类预测 - 通过分类头得到最终预测
        # [B, hidden_dim] -> [B, num_classes]
        logits = self.classifier(pooled_output)

        return logits
